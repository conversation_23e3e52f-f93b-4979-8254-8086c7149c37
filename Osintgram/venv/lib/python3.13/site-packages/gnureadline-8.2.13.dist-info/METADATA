Metadata-Version: 2.1
Name: gnureadline
Version: 8.2.13
Summary: The standard Python readline extension statically linked against the GNU readline library.
Home-page: http://github.com/ludwigschwardt/python-gnureadline
Maintainer: <PERSON>, <PERSON><PERSON><PERSON>
Maintainer-email: <EMAIL>, <EMAIL>
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: End Users/Desktop
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Programming Language :: C
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 3
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Description-Content-Type: text/x-rst
License-File: LICENSE

Stand-alone GNU readline module
===============================

.. image:: https://img.shields.io/github/actions/workflow/status/ludwigschwardt/python-gnureadline/test.yaml?branch=main
   :alt: GitHub Workflow Status
   :target: https://github.com/ludwigschwardt/python-gnureadline/actions/workflows/test.yaml

Do I need this package?
-----------------------

Do the following quick check::

  python -c "import readline; print(readline.__doc__)"

If the output is::

  Importing this module enables command line editing using GNU readline.

then you already have GNU Readline and you probably don't need this package
(unless you know what you are doing!). However, if the output is::

  Importing this module enables command line editing using libedit readline.

then you've come to the right place.

Still interested?
-----------------

Some Posix platforms such as macOS do not ship with `GNU Readline`_ installed.
Readline is licensed under the GPL, which makes it hard to distribute with
proprietary software. A popular alternative is NetBSD's `Editline`_ (libedit)
library which has a less restrictive BSD license. If you install Python on
macOS via a popular open-source package manager such as Homebrew or MacPorts,
you'll get a readline extension module that calls libedit internally (even
though it's confusingly still called "readline"!).

While a lot of effort has gone into making GNU Readline and Editline
interchangeable within Python, they are not fully equivalent. If you want
proper Readline support, this module provides it by bundling the standard
Python readline module with the GNU Readline source code, which is compiled
and statically linked to it. The end result is a package which is simple to
install and only requires the system-dependent ncurses library.

The module is called *gnureadline* so as not to clash with the existing
readline module in the standard library. It supports two general needs:

Code that explicitly imports readline
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

A typical use case is to override readline in your code like this:

.. code:: python

  try:
      import gnureadline as readline
  except ImportError:
      import readline

Tab completion in the standard interactive Python shell
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

The above trick does not fix tab completion in the Python shell because by
the time the shell prints its first output to the screen, it's too late...
One solution is to put this workaround in one of the customization modules
imported by the `site`_ module early on during the startup process.

This is conveniently done for you by installing *gnureadline* and running::

  <python> -m override_readline

where *<python>* is the specific Python interpreter you want to fix
(for example *python3*). The script first tries to add the workaround to
*usercustomize* and then falls back to *sitecustomize* if the user site is
not enabled (for example in virtualenvs). If you want to go straight to
*sitecustomize*, add the standard *-s* option::

  <python> -s -m override_readline

The script explains in detail what it is doing and also refuses to install
the workaround twice. Another benefit of *override_readline* is that the
interactive Python interpreter gains a helpful reminder on startup, like::

  Python 3.12.2 (main, Apr 17 2024, 20:25:57) [Clang 15.0.0 (clang-1500.0.40.1)] on darwin
  Type "help", "copyright", "credits" or "license" for more information.
  Using GNU readline instead of the default readline (see sitecustomize.py)
  >>>

You don't have to run the *override_readline* script if *gnureadline* was
installed as a dependency of another package. It's only there to help you fix
tab completion in the standard Python shell.

While *usercustomize* and *sitecustomize* are associated with a specific
Python version, you can also fix tab completion for all Python versions
by adding the workaround to the *PYTHONSTARTUP* file (e.g. *~/.pythonrc*).
This requires some extra setup as seen in this `example pythonrc`_, which also
shows a way to maintain separate history files for libreadline and libedit.
The *PYTHONSTARTUP* file only affects the interactive shell, while
user / site customization affects general scripts using readline as well.
The Python Tutorial has a `section`_ describing these customization options.

**Please take note that** `IPython`_ **does not depend on gnureadline for tab
completion anymore. Since version 5.0 it uses** `prompt_toolkit`_ **instead.**

Versions
--------

The module can be used with both Python 2.x and 3.x, and has been tested with
Python versions 2.6, 2.7, and 3.2 to 3.13. The first three numbers of the
module version reflect the version of the underlying GNU Readline library
(major, minor and patch level), while any additional fourth number
distinguishes different module updates based on the same Readline library.

The latest development version is available from the `GitHub repository`_.

If you are using Windows, which also ships without GNU Readline, you might
want to consider using the `pyreadline3`_ module instead, which is a readline
replacement written in pure Python that interacts with the Windows clipboard.

.. _GNU Readline: http://www.gnu.org/software/readline/
.. _Editline: http://www.thrysoee.dk/editline/
.. _site: https://docs.python.org/library/site.html
.. _example pythonrc: https://github.com/ludwigschwardt/python-gnureadline/issues/62#issuecomment-1724103579
.. _section: https://python.readthedocs.io/en/latest/tutorial/appendix.html#interactive-mode
.. _IPython: http://ipython.org/
.. _prompt_toolkit: http://python-prompt-toolkit.readthedocs.io/en/stable/
.. _GitHub repository: http://github.com/ludwigschwardt/python-gnureadline
.. _pyreadline3: http://pypi.python.org/pypi/pyreadline3


History
=======

8.2.13 (2024-10-18)
-------------------
* #70, #76: Supports Python 3.13 (including free-threaded version)
* #74, #75: Fix readline override to work with python-build-standalone dist
* Uses Python 3.13.0 readline.c (commit 417dd3a, 2024-05-06)
* Updated to build against readline 8.2 (patch-level 13)

8.2.10 (2024-06-10)
-------------------
* #61, #62, #65, #69: Supports Python 3.11 and 3.12, but not yet 3.13
* New readline override mechanism via site customization
* Overhaul documentation and tests to reflect this
* Upgrade GitHub Actions to use macos-13 and macos-14 native builds
* Uses Python 3.12.1 readline.c (commit 7225a01, 2023-11-28), also OK for 3.11
* Uses Python 3.10.9 readline.c (commit 5074c35, 2022-10-27), also OK for 3.9
* Updated to build against readline 8.2 (patch-level 10)

8.1.2 (2022-06-14)
------------------
* #57, #58: Now supports arm64 architecture (Apple M1, Raspberry Pi)
* Moved CI from Travis + multibuild to GitHub Actions + cibuildwheel
* Uses Python 3.11.0a7 readline.c (commit aac29af, 2021-10-13), OK for 3.10, 3.9
* Uses Python 3.8.8 readline.c (commit f9d7c12, 2021-02-16), also OK for 3.7
* Uses Python 2.7.18 readline.c (commit f32bcf8, 2019-11-07)
* Updated to build against readline 8.1 (patch-level 2)

8.0.0 (2019-07-10)
------------------

* Uses Python 3.7.4 readline.c (commit ef10f88, 2019-03-20), also OK for 3.8
* Uses Python 3.6.8 readline.c (commit 25555e0, 2018-12-08), also OK for 3.5
* Uses Python 3.4.4 readline.c (commit 7462b64, 2015-11-02)
* Uses Python 2.7.16 readline.c (commit 89b5ea2, 2018-12-19)
* Updated to build against readline 8.0

6.3.8 (2017-10-20)
------------------

* #42, #44: Address compiler issues (avoid Cygwin, fix multi-arch on gcc)
* #40: Make GPLv3 license explicit
* #39: Look for bash shell in more places
* Uses Python 2.x readline.c from hg 2.7 branch (95814:192f9efe4a38)
* Uses Python 3.x readline.c from hg 3.4 / 3.5 branch (95813:ec6ed10d611e)
* Updated to build against readline 6.3 (patch-level 8)

6.3.3 (2014-04-08)
------------------

* Major rework of OS X build process (detect arches, no custom flags)
* #20, #22, #28: Various issues addressed by new streamlined build
* #28: Use $CC or cc to compile libreadline instead of default gcc
* #35: Workaround for clang from Xcode 5.1 and Mac OS X 10.9.2
* Uses Python 3.4 readline.c from hg 3.4 branch (89086:3110fb3095a2)
* Updated to build against readline 6.3 (patch-level 3)

6.2.5 (2014-02-19)
------------------

* Renamed module to *gnureadline* to improve installation with pip
* #23, #25-27, #29-33: Tweaks and package reworked to gnureadline
* Uses Python 2.x readline.c from hg 2.7 branch (89084:6b10943a5916)
* Uses Python 3.x readline.c from hg 3.3 branch (89085:6adac0d9b933)
* Updated to build against readline 6.2 (patch-level 5)

******* (2012-10-22)
--------------------

* #21: Fixed building on Python.org 3.3 / Mac OS 10.8

6.2.4 (2012-10-17)
------------------

* #15: Improved detection of compilers before Xcode 4.3
* Uses Python 3.x readline.c from v3.3.0 tag (changeset 73997)
* Updated to build against readline 6.2 (patch-level 4)

6.2.2 (2012-02-24)
------------------

* #14: Fixed compilation with Xcode 4.3 on Mac OS 10.7
* Updated to build against readline 6.2 (patch-level 2)

6.2.1 (2011-08-31)
------------------

* #10: Fixed '_emacs_meta_keymap' missing symbol on Mac OS 10.7
* #7: Fixed SDK version check to work with Mac OS 10.7 and later
* Uses Python 2.x readline.c from release27-maint branch (r87358)
* Uses Python 3.x readline.c from release32-maint branch (r88446)

6.2.0 (2011-06-02)
------------------

* #5: Removed '-arch ppc' on Mac OS 10.6, as Snow Leopard supports Intel only
* Updated to build against readline 6.2 (patch-level 1)

6.1.0 (2010-09-20)
------------------

* Changed version number to reflect readline version instead of Python version
* #4: Updated to build against readline 6.1 (patch-level 2)
* #2: Python 3 support
* Uses Python 2.x readline.c from release27-maint branch (r83672)
* Uses Python 3.x readline.c from r32a2 tag (r84541)
* Source code moved to GitHub
* Additional maintainer: Sridhar Ratnakumar

2.6.4 (2009-11-26)
------------------

* Added -fPIC to compiler flags to fix linking error on 64-bit Ubuntu
* Enabled all readline functionality specified in pyconfig.h macros
* Uses readline.c from Python svn trunk (r75725), which followed 2.6.4 release
* Patched readline.c to replace Py_XDECREF calls with the safer Py_CLEAR
* Fixed compilation error on Mac OS 10.4 with XCode older than version 2.4

2.6.1 (2009-11-18)
------------------

* Updated package to work with Mac OS 10.6 (Snow Leopard), which ships with
  Python 2.6.1
* Uses readline.c from Python 2.6.1 release
* Backported "spurious trailing space" bugfix from Python svn trunk (see e.g.
  https://bugs.launchpad.net/python/+bug/470824 for details on bug)
* Updated to build against readline 6.0 (patch-level 4)
* Now builds successfully on Linux (removed Mac-specific flags in this case),
  and still supports Mac OS 10.4 and 10.5

2.5.1 (2008-05-28)
------------------

* Updated package to work with Mac OS 10.5 (Leopard), which ships with Python
  2.5.1
* Uses readline.c from Python 2.5.1 release
* Updated to build against readline 5.2 (patch-level 12)
* New maintainer: Ludwig Schwardt

2.4.2 (2005-12-26)
------------------

* Original package by Bob Ippolito, supporting Python 2.3 / 2.4 on Mac OS 10.3
  (Panther) and 10.4 (Tiger)
* Builds against readline 5.1
