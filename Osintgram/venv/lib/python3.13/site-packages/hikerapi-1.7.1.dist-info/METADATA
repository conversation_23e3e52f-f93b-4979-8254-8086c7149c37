Metadata-Version: 2.4
Name: hikerapi
Version: 1.7.1
Summary: hikerapi client, for Python 3.
Home-page: https://hikerapi.com
Author: Hikerapi
Author-email: <EMAIL>
License: MIT
Keywords: instagram private api,instagram-private-api,instagram api,instagram-api,instagram,instagram-scraper,instagram-client,instagram-stories,instagram-feed,instagram-reels,instagram-insights,downloader,uploader,videos,photos,albums,igtv,reels,stories,pictures,instagram-user-photos,instagram-photos,instagram-metadata,instagram-downloader,instagram-uploader
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: httpx>=0.23.0
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Hikerapi client, for Python 3

## Installation

```
pip install hikerapi
```

## Usage

Create a token https://hikerapi.com/tokens and copy "Access key"

```python
from hikerapi import Client

cl = Client(token="<ACCESS_KEY>")
user = cl.user_by_username_v2("instagram")
print(user)
```

```python
from hikerapi import AsyncClient

cl = AsyncClient(token="<ACCESS_KEY>")
user = await cl.user_by_username_v2("instagram")
print(user)
```

## Run tests

```
HIKERAPI_TOKEN=<token> pytest -v tests.py

HIKERAPI_TOKEN=<token> pytest -v tests.py::test_search_music
```
