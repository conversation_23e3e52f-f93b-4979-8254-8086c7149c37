Metadata-Version: 2.4
Name: geographiclib
Version: 2.1
Summary: The geodesic routines from GeographicLib
Home-page: https://geographiclib.sourceforge.io/Python/
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Project-URL: Source Code, https://github.com/geographiclib/geographiclib-python
Project-URL: Documentation, https://geographiclib.sourceforge.io/Python/doc/
Project-URL: Download, https://pypi.org/project/geographiclib/
Keywords: gis,geographical,earth,distance,geodesic
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Science/Research
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Topic :: Scientific/Engineering :: GIS
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Dynamic: license-file

# Python implementation of the geodesic routines in GeographicLib

This is a library to solve geodesic problems on an ellipsoid model of
the earth.

Licensed under the MIT/X11 License; see
[LICENSE.txt](https://geographiclib.sourceforge.io/LICENSE.txt).

The algorithms are documented in

* C. F. F. Karney,
  [Algorithms for geodesics](https://doi.org/10.1007/s00190-012-0578-z),
  J. Geodesy **87**(1), 43–55 (2013);
  [Addenda](https://geographiclib.sourceforge.io/geod-addenda.html).

The documentation for this package is in
https://geographiclib.sourceforge.io/Python/doc
