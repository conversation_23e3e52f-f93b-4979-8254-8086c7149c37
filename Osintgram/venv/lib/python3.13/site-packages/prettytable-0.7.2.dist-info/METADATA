Metadata-Version: 2.4
Name: prettytable
Version: 0.7.2
Summary: A simple Python library for easily displaying tabular data in a visually appealing ASCII table format
Home-page: http://code.google.com/p/prettytable
Author: <PERSON>
Author-email: <EMAIL>
License: BSD (3 clause)
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.4
Classifier: Programming Language :: Python :: 2.5
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: BSD License
Classifier: Topic :: Text Processing
License-File: COPYING
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: home-page
Dynamic: license
Dynamic: license-file
Dynamic: summary
