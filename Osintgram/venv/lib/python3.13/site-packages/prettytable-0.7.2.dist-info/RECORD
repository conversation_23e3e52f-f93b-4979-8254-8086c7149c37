__pycache__/prettytable.cpython-313.pyc,,
prettytable-0.7.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
prettytable-0.7.2.dist-info/METADATA,sha256=JBnRnVHNJ1_949bOKQnY88mP0Aj3yYz7hTpLsmGgdME,826
prettytable-0.7.2.dist-info/RECORD,,
prettytable-0.7.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
prettytable-0.7.2.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
prettytable-0.7.2.dist-info/licenses/COPYING,sha256=V6M4EKZ_sXjT0pGBiwg9Bth81c6-7_2g7Br5pL-0l24,1613
prettytable-0.7.2.dist-info/top_level.txt,sha256=UooJrNm6ias54LTjladXtdbjC1Nt9sgd_1t_24UXBTQ,12
prettytable.py,sha256=fbMCGSVlSL5EitcRVr_2iIdzrI80AY1Ow7fZKElIYjE,54204
